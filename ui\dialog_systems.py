# -*- coding: utf-8 -*-
"""
Dialog Systems Module

Centralized user input dialogs for EEI analysis workflow.
Handles parameter selection, depth ranges, target logs, and workflow decisions.

This module extracts all dialog-related functionality from the main application
to improve maintainability and enable better testing.
"""

import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import numpy as np
import logging

logger = logging.getLogger(__name__)


class DialogSystems:
    """
    Centralized dialog management for EEI analysis workflow.

    This class manages all user input dialogs including:
    - Analysis type and parameter selection
    - Depth range selection with Excel integration
    - Target log selection interface
    - Post-analysis action selection
    """

    def __init__(self):
        """Initialize dialog systems with state management."""
        self.last_selections = {}  # Cache user selections for consistency
        self.state = {}  # Shared state for dialog coordination

    def get_analysis_type_and_parameters(self):
        """
        Prompt the user to select the analysis type (EEI, CPEI, or PEIL) and associated parameters.

        Returns:
            tuple: (analysis_method, calcmethod, k_method, k_value)
                - analysis_method: 1=EEI, 2=CPEI, 3=PEIL
                - calcmethod: EEI calculation method (1-3) or None
                - k_method: K value determination method (1-2) or None
                - k_value: Constant k value or None
        """
        root = tk.Tk()
        root.withdraw()

        # Ask user to select analysis type
        analysis_method = simpledialog.askinteger(
            "Analysis Type Selection",
            "Select the analysis type:\n"
            "1: EEI (Extended Elastic Impedance) Analysis\n"
            "2: CPEI (Compressional Poisson Elastic Impedance) Analysis\n"
            "3: PEIL (Poisson Elastic Impedance Log) Analysis",
            minvalue=1, maxvalue=3
        )

        if analysis_method not in [1, 2, 3]:
            print("Invalid analysis type. Using default EEI analysis.")
            analysis_method = 1

        # Initialize return values
        calcmethod = None
        k_method = None
        k_value = None

        if analysis_method == 1:  # EEI Analysis
            print("Selected: EEI (Extended Elastic Impedance) Analysis")

            # Ask for EEI calculation method
            calcmethod = simpledialog.askinteger(
                "EEI Calculation Method",
                "Enter the calcmethod for EEI calculation:\n"
                "1: EEI using Vp, Vs, density\n"
                "2: EEI using AI, SI, density\n"
                "3: EEI using AI, SI, with omitted density",
                minvalue=1, maxvalue=3
            )
            if calcmethod not in [1, 2, 3]:
                print("Invalid calcmethod. Using default value 3.")
                calcmethod = 3

            # Ask user how to determine k value
            k_method = simpledialog.askinteger(
                "K Value Method",
                "How would you like to determine k value?\n"
                "1: Calculate from average of logs (Vs/Vp)²\n"
                "2: Enter a constant value manually",
                minvalue=1, maxvalue=2
            )

            # If user chose to enter a constant value
            if k_method == 2:
                k_value = simpledialog.askfloat(
                    "K Value Input",
                    "Enter constant k value (typically between 0.1 and 0.5):",
                    minvalue=0.01, maxvalue=1.0
                )
                if k_value is None:
                    print("Invalid k value. Using default method (calculate from logs).")
                    k_method = 1

        elif analysis_method == 2:  # CPEI Analysis
            print("Selected: CPEI (Compressional Poisson Elastic Impedance) Analysis")
            print("CPEI will search for optimal n (0.1-2.0) and phi (-90° to +90°) parameters.")

        elif analysis_method == 3:  # PEIL Analysis
            print("Selected: PEIL (Poisson Elastic Impedance Log) Analysis")
            print("PEIL will search for optimal n (0.1-2.0) and phi (-90° to +90°) parameters.")

        # Cache the selection for consistency
        self.last_selections['analysis_type'] = {
            'method': analysis_method,
            'calcmethod': calcmethod,
            'k_method': k_method,
            'k_value': k_value
        }

        return analysis_method, calcmethod, k_method, k_value

    def show_next_action_dialog(self):
        """
        Show a dialog asking the user what to do after analysis completion.

        Returns:
            str: 'restart' to start new analysis, 'exit' to exit program
        """
        root = tk.Tk()
        root.withdraw()

        # Create custom dialog
        dialog = tk.Toplevel()
        dialog.title("Analysis Complete")
        dialog.geometry("400x200")
        dialog.grab_set()  # Make dialog modal
        dialog.resizable(False, False)

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = {'action': 'exit'}

        # Main message
        message_frame = tk.Frame(dialog)
        message_frame.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)

        title_label = tk.Label(
            message_frame,
            text="🎉 Analysis Complete!",
            font=("Arial", 14, "bold"),
            fg="green"
        )
        title_label.pack(pady=(0, 10))

        message_label = tk.Label(
            message_frame,
            text="The EEI cross-correlation analysis has been completed successfully.\n"
                 "What would you like to do next?",
            font=("Arial", 10),
            justify=tk.CENTER,
            wraplength=350
        )
        message_label.pack(pady=(0, 20))

        # Button frame
        button_frame = tk.Frame(dialog)
        button_frame.pack(pady=(0, 20), padx=20, fill=tk.X)

        def restart_analysis():
            result['action'] = 'restart'
            dialog.destroy()

        def exit_program():
            result['action'] = 'exit'
            dialog.destroy()

        # Restart button
        restart_btn = tk.Button(
            button_frame,
            text="🔄 Start New Analysis",
            command=restart_analysis,
            bg="#4CAF50",
            fg="white",
            font=("Arial", 10, "bold"),
            padx=20,
            pady=5
        )
        restart_btn.pack(side=tk.LEFT, padx=(0, 10), fill=tk.X, expand=True)

        # Exit button
        exit_btn = tk.Button(
            button_frame,
            text="🚪 Exit Program",
            command=exit_program,
            bg="#f44336",
            fg="white",
            font=("Arial", 10, "bold"),
            padx=20,
            pady=5
        )
        exit_btn.pack(side=tk.RIGHT, padx=(10, 0), fill=tk.X, expand=True)

        # Handle window close event
        dialog.protocol("WM_DELETE_WINDOW", exit_program)

        # Wait for dialog to close
        dialog.wait_window()

        return result['action']

    def get_module_info(self):
        """
        Get information about this dialog systems module.

        Returns:
            dict: Module information including version, functions, etc.
        """
        return {
            'module_name': 'Dialog Systems',
            'version': '1.0.0',
            'functions': [
                'get_analysis_type_and_parameters',
                'show_next_action_dialog',
                'get_target_log',
                'get_depth_ranges',
                'select_alternative_mnemonic'
            ],
            'status': 'Phase 2C - Partially Complete',
            'extracted_functions': 2,
            'total_functions': 5,
            'completion_percentage': 40
        }


# Global instance for backward compatibility
dialog_systems = DialogSystems()


# Legacy wrapper functions for backward compatibility
def get_analysis_type_and_parameters():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.get_analysis_type_and_parameters()


def show_next_action_dialog():
    """Legacy wrapper for backward compatibility."""
    return dialog_systems.show_next_action_dialog()
